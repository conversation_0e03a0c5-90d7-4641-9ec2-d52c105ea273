<?php
// Тестов скрипт за проверка на зареждането на модела

// Включване на конфигурацията
require_once('admin/config.php');

// Преопределяне на DIR_THEME за Windows
if (!defined('DIR_THEME_WINDOWS')) {
    define('DIR_THEME_WINDOWS', 'system/storage/theme/');
}

// Включване на autoloader-а
if (file_exists(DIR_THEME_WINDOWS . 'autoloader.php')) {
    require_once(DIR_THEME_WINDOWS . 'autoloader.php');
} else {
    echo "Autoloader not found at: " . DIR_THEME_WINDOWS . 'autoloader.php' . "<br>";
}

echo "<h2>Тест на зареждането на модела</h2>";

// Проверка на пътищата
echo "<h3>Проверка на пътищата:</h3>";
echo "DIR_THEME (Linux): " . (defined('DIR_THEME') ? DIR_THEME : 'NOT DEFINED') . "<br>";
echo "DIR_THEME_WINDOWS: " . DIR_THEME_WINDOWS . "<br>";
echo "Backend Model Path: " . DIR_THEME_WINDOWS . 'Backend/Model/Catalog/Product.php' . "<br>";

// Проверка дали файлът съществува
$modelFile = DIR_THEME_WINDOWS . 'Backend/Model/Catalog/Product.php';
echo "Model file exists: " . (file_exists($modelFile) ? 'YES' : 'NO') . "<br>";

if (file_exists($modelFile)) {
    echo "<h3>Включване на файла:</h3>";
    
    try {
        include_once($modelFile);
        echo "File included successfully<br>";
        
        // Проверка на класа
        $className = 'Theme25\\Backend\\Model\\Catalog\\Product';
        echo "Class name: $className<br>";
        echo "Class exists: " . (class_exists($className) ? 'YES' : 'NO') . "<br>";
        
        if (class_exists($className)) {
            echo "<h3>Методи на класа:</h3>";
            $methods = get_class_methods($className);
            echo "Available methods: " . implode(', ', $methods) . "<br>";
            
            // Проверка дали има getProduct метод
            echo "Has getProduct method: " . (in_array('getProduct', $methods) ? 'YES' : 'NO') . "<br>";
            
            // Опит за създаване на инстанция
            echo "<h3>Създаване на инстанция:</h3>";
            try {
                // Създаване на mock registry
                $registry = new stdClass();
                $registry->db = null; // Mock DB
                
                $instance = new $className($registry);
                echo "Instance created successfully<br>";
                echo "Instance type: " . get_class($instance) . "<br>";
                
                // Проверка на методите на инстанцията
                $instanceMethods = get_class_methods($instance);
                echo "Instance methods: " . implode(', ', $instanceMethods) . "<br>";
                
            } catch (Exception $e) {
                echo "Error creating instance: " . $e->getMessage() . "<br>";
            }
        }
        
    } catch (Exception $e) {
        echo "Error including file: " . $e->getMessage() . "<br>";
    }
} else {
    echo "Model file does not exist!<br>";
}

// Проверка на стандартния модел
echo "<h3>Проверка на стандартния модел:</h3>";
$standardModelFile = DIR_APPLICATION . 'model/catalog/product.php';
echo "Standard model file: $standardModelFile<br>";
echo "Standard model exists: " . (file_exists($standardModelFile) ? 'YES' : 'NO') . "<br>";

if (file_exists($standardModelFile)) {
    try {
        include_once($standardModelFile);
        $standardClassName = 'ModelCatalogProduct';
        echo "Standard class exists: " . (class_exists($standardClassName) ? 'YES' : 'NO') . "<br>";
        
        if (class_exists($standardClassName)) {
            $standardMethods = get_class_methods($standardClassName);
            echo "Standard methods: " . implode(', ', $standardMethods) . "<br>";
            echo "Standard has getProduct: " . (in_array('getProduct', $standardMethods) ? 'YES' : 'NO') . "<br>";
        }
    } catch (Exception $e) {
        echo "Error with standard model: " . $e->getMessage() . "<br>";
    }
}

// Проверка на базовия Model клас
echo "<h3>Проверка на базовия Model клас:</h3>";
$modelClassFile = DIR_SYSTEM . 'engine/model.php';
echo "Base Model file: $modelClassFile<br>";
echo "Base Model exists: " . (file_exists($modelClassFile) ? 'YES' : 'NO') . "<br>";

if (file_exists($modelClassFile)) {
    try {
        include_once($modelClassFile);
        echo "Base Model class exists: " . (class_exists('Model') ? 'YES' : 'NO') . "<br>";
        
        if (class_exists('Model')) {
            $baseMethods = get_class_methods('Model');
            echo "Base Model methods: " . implode(', ', $baseMethods) . "<br>";
        }
    } catch (Exception $e) {
        echo "Error with base Model: " . $e->getMessage() . "<br>";
    }
}
?>
