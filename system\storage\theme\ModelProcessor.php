<?php

namespace Theme25;

/**
 * Клас за обработка на заявки към модели
 */
class ModelProcessor extends BaseProcessor {

    /**
     * Конструктор
     *
     * @param \Registry $registry Регистър с обекти
     * @param string $controllerPath Път до контролерите ('Backend' или 'Frontend')
     */
    public function __construct($registry, $controllerPath = 'Frontend') {
        parent::__construct($registry, $controllerPath);
    }

    /**
     * Обработва заявка към модел
     *
     * @param string $route Път към модела
     * @return mixed Резултат от обработката
     */
    public function process($route) {
        // Проверка дали моделът трябва да използва втората база данни
        $useSecondDb = $this->shouldUseSecondDb($route);

        // Запазване на оригиналната база данни
        $originalDb = $this->switchDatabaseIfNeeded($useSecondDb);

        try {
            // Проверка дали моделът вече е зареден
            if (!$this->isModelLoaded($route)) {
                $this->loadModel($route, $useSecondDb, $originalDb);
            }

            // Възстановяване на оригиналната база данни
            $this->restoreDatabaseIfNeeded($useSecondDb, $originalDb);

            return null;
        } catch (\Exception $e) {
            // Възстановяване на оригиналната база данни в случай на грешка
            $this->restoreDatabaseIfNeeded($useSecondDb, $originalDb);

            throw $e;
        }
    }

    /**
     * Проверява дали моделът вече е зареден
     *
     * @param string $route Път към модела
     * @return bool Дали моделът е зареден
     */
    protected function isModelLoaded($route) {
        return $this->registry->has('model_' . str_replace('/', '_', $route));
    }

    /**
     * Превключва базата данни, ако е необходимо
     *
     * @param bool $useSecondDb Дали да се използва втората база данни
     * @return \DB|null Оригиналната база данни или null
     */
    protected function switchDatabaseIfNeeded($useSecondDb) {
        if ($useSecondDb) {
            $originalDb = $this->registry->get('db');
            $this->registry->set('db', self::$secondDb);

            // Указване, че в момента се използва втората база данни
            $this->switchToSecondDatabase($this->registry);

            return $originalDb;
        }

        return null;
    }

    /**
     * Възстановява оригиналната база данни, ако е необходимо
     *
     * @param bool $useSecondDb Дали е използвана втората база данни
     * @param \DB|null $originalDb Оригиналната база данни
     * @return void
     */
    protected function restoreDatabaseIfNeeded($useSecondDb, $originalDb) {
        if ($useSecondDb && $originalDb !== null) {
            $this->registry->set('db', $originalDb);

            // Указване, че в момента се използва първата (основната) база данни
            $this->switchToFirstDatabase($this->registry);
        }
    }

    /**
     * Зарежда модел
     *
     * @param string $route Път към модела
     * @param bool $useSecondDb Дали се използва втората база данни
     * @param \DB|null $originalDb Оригиналната база данни
     * @return void
     * @throws \Exception Ако моделът не може да бъде зареден
     */
    protected function loadModel($route, $useSecondDb, $originalDb) {
        // Проверка за персонализиран модел в темата
        $themeModelFile = $this->getThemeModelPath($route);
        $themeModelClass = $this->getThemeModelClass($route);

        // Debug информация
        // if ($route === 'catalog/product') {
        //     F()->log->developer("Loading model: $route", __FILE__, __LINE__);
        //     F()->log->developer("Theme model file: $themeModelFile", __FILE__, __LINE__);
        //     F()->log->developer("Theme model class: $themeModelClass", __FILE__, __LINE__);
        //     F()->log->developer("File exists: " . (file_exists($themeModelFile) ? 'YES' : 'NO'), __FILE__, __LINE__);
        // }

        if (file_exists($themeModelFile)) {
            $this->loadThemeModel($route, $themeModelFile, $themeModelClass);
        } else {
            // Проверка за стандартен модел
            $file = $this->getStandardModelPath($route);
            $class = $this->getStandardModelClass($route);

            if (file_exists($file)) {
                $this->loadStandardModel($route, $file, $class);
            } else {
                // Възстановяване на оригиналната база данни в случай на грешка
                $this->restoreDatabaseIfNeeded($useSecondDb, $originalDb);

                throw new \Exception('Error: Could not load model ' . $route . '!');
            }
        }
    }

    /**
     * Връща пътя до модела в темата
     *
     * @param string $route Път към модела
     * @return string Път до файла на модела в темата
     */
    protected function getThemeModelPath($route) {
        $route = $this->routeToClass($route);
        return DIR_THEME . $this->controllerPath . '/Model/' . $route . '.php';
    }

    protected function routeToClass($route) {
        $parts = explode('/', $route);

        // Трансформиране на всяка част - първа буква главна, останалите малки
        $transformedParts = [];
        foreach ($parts as $part) {
            $transformedParts[] = ucfirst(strtolower($part));
        }

        $class = implode('/', $transformedParts);
        return $class;
    }

    /**
     * Връща името на класа на модела в темата
     *
     * @param string $route Път към модела
     * @return string Име на класа на модела в темата
     */
    protected function getThemeModelClass($route) {
        $route = $this->routeToClass($route);
        return 'Theme25\\' . $this->controllerPath . '\\Model\\' . str_replace('/', '\\', $route);
    }

    /**
     * Връща пътя до стандартния модел
     *
     * @param string $route Път към модела
     * @return string Път до файла на стандартния модел
     */
    protected function getStandardModelPath($route) {
        return DIR_APPLICATION . 'model/' . $route . '.php';
    }

    /**
     * Връща името на класа на стандартния модел
     *
     * @param string $route Път към модела
     * @return string Име на класа на стандартния модел
     */
    protected function getStandardModelClass($route) {
        return 'Model' . preg_replace('/[^a-zA-Z0-9]/', '', $route);
    }

    /**
     * Зарежда модел от темата
     *
     * @param string $route Път към модела
     * @param string $themeModelFile Път до файла на модела в темата
     * @param string $themeModelClass Име на класа на модела в темата
     * @return void
     */
    protected function loadThemeModel($route, $themeModelFile, $themeModelClass) {
        include_once($themeModelFile);

        // Debug информация
        // if ($route === 'catalog/product') {
        //     F()->log->developer("Loading theme model: $themeModelClass", __FILE__, __LINE__);
        //     F()->log->developer("Class exists: " . (class_exists($themeModelClass) ? 'YES' : 'NO'), __FILE__, __LINE__);
        //     if (class_exists($themeModelClass)) {
        //         $methods = get_class_methods($themeModelClass);
        //         F()->log->developer("Available methods: " . implode(', ', $methods), __FILE__, __LINE__);
        //     }
        // }

        // Създаване на прокси обект за модела
        $proxy = new \Proxy();

        // Добавяне на методите на модела към прокси обекта
        foreach (get_class_methods($themeModelClass) as $method) {
            $proxy->{$method} = $this->createCallback($this->registry, $route . '/' . $method, $themeModelClass);
        }

        $this->registry->set('model_' . str_replace('/', '_', (string)$route), $proxy);
    }

    /**
     * Зарежда стандартен модел
     *
     * @param string $route Път към модела
     * @param string $file Път до файла на стандартния модел
     * @param string $class Име на класа на стандартния модел
     * @return void
     */
    protected function loadStandardModel($route, $file, $class) {
        include_once($file);

        // Създаване на прокси обект за модела
        $proxy = new \Proxy();

        // Добавяне на методите на модела към прокси обекта
        foreach (get_class_methods($class) as $method) {
            $proxy->{$method} = $this->createCallback($this->registry, $route . '/' . $method);
        }

        $this->registry->set('model_' . str_replace('/', '_', (string)$route), $proxy);
    }

    /**
     * Създава callback функция за извикване на методи на модела
     *
     * @param \Registry $registry Регистър с обекти
     * @param string $route Път към метода на модела
     * @param string|null $themeModelClass Име на класа на модела в темата (ако има такъв)
     * @return callable Callback функция
     */
    protected function createCallback($registry, $route, $themeModelClass = null) {
        return function($args) use($registry, $route, $themeModelClass) {
            static $model;

            // Санитизиране на пътя и задаване на тригер
            $route = $this->sanitizeRoute($route);
            $trigger = $route;

            // Обработка на събитията преди извикване на метода
            $preEventResult = $this->triggerModelPreEvents($registry, $trigger, $route, $args);

            if ($preEventResult !== null) {
                $output = $preEventResult;
            } else {
                // Инициализиране на модела и извикване на метода
                $model = $this->initializeModel($registry, $model, $route, $themeModelClass);
                $output = $this->callModelMethod($registry, $model, $route, $args);
            }

            // Обработка на събитията след извикване на метода
            $postEventResult = $this->triggerModelPostEvents($registry, $trigger, $route, $args, $output);

            if ($postEventResult !== null) {
                $output = $postEventResult;
            }

            return $output;
        };
    }

    /**
     * Санитизира пътя към метода на модела
     *
     * @param string $route Път към метода на модела
     * @return string Санитизиран път
     */
    protected function sanitizeRoute($route) {
        return preg_replace('/[^a-zA-Z0-9_\/]/', '', (string)$route);
    }

    /**
     * Обработва събитията преди извикване на метода на модела
     *
     * @param \Registry $registry Регистър с обекти
     * @param string $trigger Оригинален тригер
     * @param string $route Път към метода на модела
     * @param array $args Аргументи за метода
     * @return mixed Резултат от събитието или null
     */
    protected function triggerModelPreEvents($registry, $trigger, &$route, &$args) {
        $result = $registry->get('event')->trigger('model/' . $trigger . '/before', array(&$route, &$args));

        if ($result && !$result instanceof \Exception) {
            return $result;
        }

        return null;
    }

    /**
     * Инициализира модела
     *
     * @param \Registry $registry Регистър с обекти
     * @param array $model Масив с инстанции на модели
     * @param string $route Път към метода на модела
     * @param string|null $themeModelClass Име на класа на модела в темата (ако има такъв)
     * @return array Масив с инстанции на модели
     */
    protected function initializeModel($registry, $model, $route, $themeModelClass) {
        if ($themeModelClass !== null) {
            // Използване на модел от темата
            $model = $this->initializeThemeModel($registry, $model, $route, $themeModelClass);
        } else {
            // Използване на стандартен модел
            $model = $this->initializeStandardModel($registry, $model, $route);
        }

        return $model;
    }

    /**
     * Инициализира модел от темата
     *
     * @param \Registry $registry Регистър с обекти
     * @param array $model Масив с инстанции на модели
     * @param string $route Път към метода на модела
     * @param string $themeModelClass Име на класа на модела в темата
     * @return array Масив с инстанции на модели
     */
    protected function initializeThemeModel($registry, $model, $route, $themeModelClass) {
        $key = substr($route, 0, strrpos($route, '/'));

        if (!isset($model[$key])) {
            $model[$key] = new $themeModelClass($registry);
        }

        return $model;
    }

    /**
     * Инициализира стандартен модел
     *
     * @param \Registry $registry Регистър с обекти
     * @param array $model Масив с инстанции на модели
     * @param string $route Път към метода на модела
     * @return array Масив с инстанции на модели
     */
    protected function initializeStandardModel($registry, $model, $route) {
        $class = 'Model' . preg_replace('/[^a-zA-Z0-9]/', '', substr($route, 0, strrpos($route, '/')));
        $key = substr($route, 0, strrpos($route, '/'));

        if (!isset($model[$key])) {
            $model[$key] = new $class($registry);
        }

        return $model;
    }

    /**
     * Извиква метод на модела
     *
     * @param \Registry $registry Регистър с обекти
     * @param array $model Масив с инстанции на модели
     * @param string $route Път към метода на модела
     * @param array $args Аргументи за метода
     * @return mixed Резултат от извикването на метода
     * @throws \Exception Ако методът не може да бъде извикан
     */
    protected function callModelMethod($registry, $model, $route, $args) {
        $key = substr($route, 0, strrpos($route, '/'));
        $method = substr($route, strrpos($route, '/') + 1);
        $callable = array($model[$key], $method);

        if (is_callable($callable)) {
            // Проверка дали методът трябва да използва втората база данни
            $useSecondDb = $this->shouldUseSecondDb($route);

            // Запазване на оригиналната база данни
            $originalDb = $this->switchModelDatabaseIfNeeded($registry, $useSecondDb);

            try {
                // Извикване на метода
                $output = call_user_func_array($callable, $args);

                // Възстановяване на оригиналната база данни
                $this->restoreModelDatabaseIfNeeded($registry, $useSecondDb, $originalDb);

                return $output;
            } catch (\Exception $e) {
                // Възстановяване на оригиналната база данни в случай на грешка
                $this->restoreModelDatabaseIfNeeded($registry, $useSecondDb, $originalDb);

                throw $e;
            }
        } else {
            throw new \Exception('Error: Could not call model/' . $route . '!');
        }
    }

    /**
     * Превключва базата данни за модела, ако е необходимо
     *
     * @param \Registry $registry Регистър с обекти
     * @param bool $useSecondDb Дали да се използва втората база данни
     * @return \DB|null Оригиналната база данни или null
     */
    protected function switchModelDatabaseIfNeeded($registry, $useSecondDb) {
        if ($useSecondDb) {
            $requestProcessor = $registry->get('request_processor');
            $originalDb = $registry->get('db');
            $registry->set('db', $requestProcessor->getSecondDatabase());

            // Указване, че в момента се използва втората база данни
            $this->switchToSecondDatabase($registry);

            return $originalDb;
        }

        return null;
    }

    /**
     * Възстановява оригиналната база данни за модела, ако е необходимо
     *
     * @param \Registry $registry Регистър с обекти
     * @param bool $useSecondDb Дали е използвана втората база данни
     * @param \DB|null $originalDb Оригиналната база данни
     * @return void
     */
    protected function restoreModelDatabaseIfNeeded($registry, $useSecondDb, $originalDb) {
        if ($useSecondDb && $originalDb !== null) {
            $registry->set('db', $originalDb);

            // Указване, че в момента се използва първата (основната) база данни
            $this->switchToFirstDatabase($registry);
        }
    }

    /**
     * Обработва събитията след извикване на метода на модела
     *
     * @param \Registry $registry Регистър с обекти
     * @param string $trigger Оригинален тригер
     * @param string $route Път към метода на модела
     * @param array $args Аргументи за метода
     * @param mixed $output Резултат от извикването на метода
     * @return mixed Резултат от събитието или null
     */
    protected function triggerModelPostEvents($registry, $trigger, &$route, &$args, &$output) {
        $result = $registry->get('event')->trigger('model/' . $trigger . '/after', array(&$route, &$args, &$output));

        if ($result && !$result instanceof \Exception) {
            return $result;
        }

        return null;
    }
}

