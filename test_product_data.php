<?php
// Тестов скрипт за проверка на данните за продукти

// Включване на конфигурацията
require_once('admin/config.php');

// Създаване на връзка с базата данни
try {
    $pdo = new PDO("mysql:host=" . DB_HOSTNAME . ";port=" . DB_PORT . ";dbname=" . DB_DATABASE, DB_USERNAME, DB_PASSWORD);
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    $pdo->exec("SET NAMES 'utf8'");
    
    echo "<h2>Тест на данните за продукти</h2>";
    
    // Проверка на продуктите в базата данни
    $stmt = $pdo->query("SELECT product_id, model, sku, price, quantity, minimum, status FROM " . DB_PREFIX . "product LIMIT 5");
    $products = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    echo "<h3>Продукти в базата данни:</h3>";
    if (empty($products)) {
        echo "<p>Няма продукти в базата данни.</p>";
    } else {
        echo "<table border='1' style='border-collapse: collapse;'>";
        echo "<tr><th>ID</th><th>Модел</th><th>SKU</th><th>Цена</th><th>Количество</th><th>Минимум</th><th>Статус</th></tr>";
        foreach ($products as $product) {
            echo "<tr>";
            echo "<td>" . htmlspecialchars($product['product_id']) . "</td>";
            echo "<td>" . htmlspecialchars($product['model']) . "</td>";
            echo "<td>" . htmlspecialchars($product['sku']) . "</td>";
            echo "<td>" . htmlspecialchars($product['price']) . "</td>";
            echo "<td>" . htmlspecialchars($product['quantity']) . "</td>";
            echo "<td>" . htmlspecialchars($product['minimum']) . "</td>";
            echo "<td>" . ($product['status'] ? 'Активен' : 'Неактивен') . "</td>";
            echo "</tr>";
        }
        echo "</table>";
        
        // Проверка на описанията за първия продукт
        $first_product_id = $products[0]['product_id'];
        echo "<h3>Описания за продукт ID: $first_product_id</h3>";
        
        $stmt = $pdo->prepare("SELECT * FROM " . DB_PREFIX . "product_description WHERE product_id = ?");
        $stmt->execute([$first_product_id]);
        $descriptions = $stmt->fetchAll(PDO::FETCH_ASSOC);
        
        if (empty($descriptions)) {
            echo "<p>Няма описания за този продукт.</p>";
        } else {
            echo "<table border='1' style='border-collapse: collapse;'>";
            echo "<tr><th>Език ID</th><th>Име</th><th>Тагове</th><th>Meta Title</th></tr>";
            foreach ($descriptions as $desc) {
                echo "<tr>";
                echo "<td>" . htmlspecialchars($desc['language_id']) . "</td>";
                echo "<td>" . htmlspecialchars($desc['name']) . "</td>";
                echo "<td>" . htmlspecialchars($desc['tag']) . "</td>";
                echo "<td>" . htmlspecialchars($desc['meta_title']) . "</td>";
                echo "</tr>";
            }
            echo "</table>";
        }
        
        // Проверка на промоционалните цени
        echo "<h3>Промоционални цени за продукт ID: $first_product_id</h3>";
        
        $stmt = $pdo->prepare("SELECT * FROM " . DB_PREFIX . "product_special WHERE product_id = ?");
        $stmt->execute([$first_product_id]);
        $specials = $stmt->fetchAll(PDO::FETCH_ASSOC);
        
        if (empty($specials)) {
            echo "<p>Няма промоционални цени за този продукт.</p>";
        } else {
            echo "<table border='1' style='border-collapse: collapse;'>";
            echo "<tr><th>Цена</th><th>Дата от</th><th>Дата до</th><th>Приоритет</th></tr>";
            foreach ($specials as $special) {
                echo "<tr>";
                echo "<td>" . htmlspecialchars($special['price']) . "</td>";
                echo "<td>" . htmlspecialchars($special['date_start']) . "</td>";
                echo "<td>" . htmlspecialchars($special['date_end']) . "</td>";
                echo "<td>" . htmlspecialchars($special['priority']) . "</td>";
                echo "</tr>";
            }
            echo "</table>";
        }
    }
    
    // Проверка на езиците
    echo "<h3>Налични езици:</h3>";
    $stmt = $pdo->query("SELECT language_id, name, code FROM " . DB_PREFIX . "language");
    $languages = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    if (empty($languages)) {
        echo "<p>Няма езици в базата данни.</p>";
    } else {
        echo "<table border='1' style='border-collapse: collapse;'>";
        echo "<tr><th>ID</th><th>Име</th><th>Код</th></tr>";
        foreach ($languages as $language) {
            echo "<tr>";
            echo "<td>" . htmlspecialchars($language['language_id']) . "</td>";
            echo "<td>" . htmlspecialchars($language['name']) . "</td>";
            echo "<td>" . htmlspecialchars($language['code']) . "</td>";
            echo "</tr>";
        }
        echo "</table>";
    }
    
} catch (PDOException $e) {
    echo "<p style='color: red;'>Грешка при свързване с базата данни: " . htmlspecialchars($e->getMessage()) . "</p>";
}
?>
